"""
test_icepick_run.py

Unit testing for icepick_run
"""

import unittest
from unittest import TestCase
import os
import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import MagicM<PERSON>, Mock, patch
from dice_elipy_scripts.icepick_run import cli, get_build_from_bilbo
from elipy2 import LOGGER
from elipy2.config import ConfigManager
from elipy2.bilbo import Build
from elipy2.exceptions import ELIPYException

config_path = os.path.join(os.path.dirname(__file__), "data", "elipy_test.yml")


class TestIcepickBase(unittest.TestCase):
    OPTION_TEST_SUITES = "--ts"
    OPTION_ASSETS = "--as"
    OPTION_CODE_BRANCH = "--cb"
    OPTION_CODE_CHANGELIST = "--cc"
    OPTION_DATA_CHANGELIST = "--dc"
    OPTION_DATA_BRANCH = "--db"
    OPTION_TEST_EXTRA_ARG = "--extra_arg"
    OPTION_IMPORT_AVALANCHE_STATE = "--ias"
    OPTION_NEED_GAME_SERVER = "--ngs"
    OPTION_SERVER_REGION = "--sr"
    OPTION_PACKAGE_TYPE = "--pt"
    OPTION_REGION = "-r"
    OPTION_CONFIG = "-c"
    OPTION_SERVER_CONFIG = "--sc"
    OPTION_CLIENT_BUILD_ID = "--cbi"
    OPTION_SERVER_BUILD_ID = "--sbi"
    OPTION_LICENSEEE = "-l"
    OPTION_COOK_TYPE = "--ct"
    OPTION_TEST_TYPE = "--test-type"
    OPTION_custom_tag = "--cut"
    OPTION_ADDITIONAL_TOOLS_TO_INCLUDE = "--atti"
    OPTION_CODE_P4_PORT = "--cpp"
    OPTION_P4_USER = "--pu"
    OPTION_PASSWORD = "-p"
    OPTION_NUM_TEST_RUNS = "--num-test-runs"
    FLAG_FETCH_TEST = "--ft"
    FLAG_FETCH_FROSTED = "--ff"
    FLAG_CADET_ACTIVATE_TOOLSET = "--cat"
    BOOL_ENABLE_HAILSTORM = "--eh"
    BOOL_IS_FROSTED = "--if"

    VALUE_PLATFORM = "ps4"
    VALUE_TEST_SUITE_NAME1 = "test-suite1"
    VALUE_TEST_SUITE_NAME2 = "test-suite2"
    VALUE_POOL_TYPE1 = "atf"
    VALUE_POOL_TYPE2 = "test"
    VALUE_LICENSEE1 = "ExampleGame"
    VALUE_LICENSEE2 = "FBNULLLICENSEE"
    VALUE_ARTIFACT_TEST = "Tests"
    VALUE_custom_tag = "test_folder"
    VALUE_TEST_SUITES = (
        '{"name": "'
        + VALUE_TEST_SUITE_NAME1
        + '","extra-args":["--pool-type","'
        + VALUE_POOL_TYPE1
        + '"]}'
    )
    VALUE_TEST_SUITES2 = (
        '{"name": "'
        + VALUE_TEST_SUITE_NAME2
        + '","extra-args":["--pool-type","'
        + VALUE_POOL_TYPE2
        + '"]}'
    )
    VALUE_ASSETS = "asset1"
    VALUE_CODE_BRANCH = "code-branch"
    VALUE_CODE_CHANGELIST = "123"
    VALUE_DATA_CHANGELIST = "123"
    VALUE_DATA_BRANCH = "data-branch"
    VALUE_TEST_EXTRA_ARG = "test-value"
    VALUE_SERVER_REGION = "alpha"
    VALUE_PACKAGE_TYPE = "files"
    VALUE_SERVER_PLATFORM = "server"
    VALUE_REGION = "dev"
    VALUE_CONFIG = "final"
    VALUE_SERVER_CONFIG = "final"
    VALUE_CLIENT_BUILD_ID = "\\\\client\\build\\id"
    VALUE_SERVER_BUILD_ID = "\\\\server\\build\\id"
    VALUE_ADDITIONAL_TOOLS_TO_INCLUDE_01 = "frostedtests"
    VALUE_ADDITIONAL_TOOLS_TO_INCLUDE_02 = "pipeline"
    VALUE_CODE_P4_PORT = "code_p4_port:2001"
    VALUE_P4_USER = "p4_user"
    VALUE_PASSWORD = "password"

    DEFAULT_ARGS = [
        VALUE_PLATFORM,
        OPTION_TEST_SUITES,
        VALUE_TEST_SUITES,
        OPTION_TEST_SUITES,
        VALUE_TEST_SUITES2,
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_DATA_CHANGELIST,
        VALUE_DATA_CHANGELIST,
        OPTION_ASSETS,
        VALUE_ASSETS,
        OPTION_DATA_BRANCH,
        VALUE_DATA_BRANCH,
        OPTION_TEST_EXTRA_ARG,
        VALUE_TEST_EXTRA_ARG,
        OPTION_CLIENT_BUILD_ID,
        VALUE_CLIENT_BUILD_ID,
    ]

    def setUp(self):
        self.patcher_IcepickUtils = patch(
            "dice_elipy_scripts.icepick_run.icepick.IcepickUtils", autospec=True
        )
        mock_IcepickUtils = self.patcher_IcepickUtils.start()

        self.patcher_fbcli_pullbuild = patch("elipy2.frostbite.fbcli.pullbuild", autospec=True)
        self.mock_fbcli_pullbuild = self.patcher_fbcli_pullbuild.start()

        self.mock_run_icepick = MagicMock()
        mock_IcepickUtils().run_icepick = self.mock_run_icepick

    def tearDown(self):
        self.patcher_IcepickUtils.stop()
        self.patcher_fbcli_pullbuild.stop()
        os.environ["use_fbcli"] = "False"

    def _helper_invoke_icepick_run_cli(self, extra_args=[], use_fbcli=True, catch_exceptions=True):
        if use_fbcli:
            os.environ["use_fbcli"] = "True"
        runner = CliRunner()
        return runner.invoke(cli, self.DEFAULT_ARGS + extra_args, catch_exceptions=catch_exceptions)


@patch("dice_elipy_scripts.icepick_run.save_icepick_logs", MagicMock())
@patch("dice_elipy_scripts.icepick_run.sync_specified_files_to_head", MagicMock())
@patch("dice_elipy_scripts.icepick_run.add_sentry_tags", MagicMock())
@patch("dice_elipy_scripts.icepick_run.get_environment_variables", MagicMock())
@patch("dice_elipy_scripts.icepick_run.data.DataUtils", MagicMock())
@patch.multiple(
    "dice_elipy_scripts.icepick_run.frostbite_core",
    minimum_fb_version=MagicMock(return_value=True),
    get_game_data_dir=MagicMock(return_value="\\some\\path"),
    get_licensee_id=MagicMock(return_value="some-licensee"),
)
@patch("elipy2.build_metadata.BuildMetadataManager", MagicMock())
@patch("dice_elipy_scripts.utils.autotest_utils.filer_paths", MagicMock())
@patch(
    "dice_elipy_scripts.icepick_run.local_paths.get_local_frosty_path",
    MagicMock(return_value="\\some\\path"),
)
@patch("dice_elipy_scripts.icepick_run.frosty_build_utils", MagicMock())
@patch("dice_elipy_scripts.icepick_run.set_licensee", MagicMock())
@patch("elipy2.frostbite.icepick.IcepickUtils.run_icepick_cook", MagicMock())
@patch("dice_elipy_scripts.icepick_run.icepick_clean", MagicMock())
@patch.multiple(
    "dice_elipy_scripts.icepick_run.avalanche",
    restart_avalanche=MagicMock(),
    avalanche_blocker_check=MagicMock(),
    get_temp_db_name=MagicMock(return_value="temp_db_name"),
)
@patch(
    "dice_elipy_scripts.icepick_run.icepick.IcepickUtils.settings_files_relative_to_absolute_paths",
    MagicMock(return_value=[]),
)
class TestIcepickRun2(TestIcepickBase):
    def join(path, *to_add):
        return path + "\\" + "\\".join(to_add)

    @patch(
        "dice_elipy_scripts.icepick_run.import_avalanche_data_state",
        MagicMock(return_value=["-importState", "db_name"]),
    )
    @patch("dice_elipy_scripts.icepick_run.filer", MagicMock())
    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo", MagicMock())
    @patch("os.path.join", MagicMock(side_effect=join))
    def test_run_icepick(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.VALUE_PLATFORM,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES2,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_ASSETS,
                self.VALUE_ASSETS,
                self.OPTION_DATA_BRANCH,
                self.VALUE_DATA_BRANCH,
                self.OPTION_TEST_EXTRA_ARG,
                self.VALUE_TEST_EXTRA_ARG,
                self.OPTION_CLIENT_BUILD_ID,
                self.VALUE_CLIENT_BUILD_ID,
                self.OPTION_CLIENT_BUILD_ID,
                "\\\\client\\build\\id_1",
                self.OPTION_ADDITIONAL_TOOLS_TO_INCLUDE,
                self.VALUE_ADDITIONAL_TOOLS_TO_INCLUDE_01,
                self.OPTION_ADDITIONAL_TOOLS_TO_INCLUDE,
                self.VALUE_ADDITIONAL_TOOLS_TO_INCLUDE_02,
            ],
        )
        if result.exception:
            LOGGER.info(result.exception)
        self.assertEqual(self.mock_run_icepick.call_count, 2)
        kwargs = {
            "platform": self.VALUE_PLATFORM,
            "test_suite": "\\some\\path\\" + self.VALUE_TEST_SUITE_NAME1,
            "test_group": "nogroup",
            "config": "final",
            "settings_file_list": [],
            "send_frosting_report": True,
            "lease": None,
            "build_type": "static",
            "autobuild": False,
            "run_args": [
                self.OPTION_TEST_EXTRA_ARG,
                self.VALUE_TEST_EXTRA_ARG,
                "--pool-type",
                self.VALUE_POOL_TYPE1,
            ],
            "ignore_icepick_exit_code": False,
            "cook": False,
            "extra_framework_args": None,
            "custom_test_suite_data": "code_changelist:123;data_changelist:123;",
        }
        self.mock_run_icepick.assert_any_call(**kwargs)
        kwargs["test_suite"] = "\\some\\path\\" + self.VALUE_TEST_SUITE_NAME2
        kwargs["run_args"] = [
            self.OPTION_TEST_EXTRA_ARG,
            self.VALUE_TEST_EXTRA_ARG,
            "--pool-type",
            self.VALUE_POOL_TYPE2,
        ]
        self.mock_run_icepick.assert_called_with(**kwargs)
        assert result.exit_code == 0

    @patch(
        "dice_elipy_scripts.icepick_run.import_avalanche_data_state",
        MagicMock(return_value=["-importState", "db_name"]),
    )
    @patch("dice_elipy_scripts.icepick_run.filer", MagicMock())
    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo", MagicMock())
    @patch("os.path.join", MagicMock(side_effect=join))
    def test_run_icepick_multiple_licensee(self):
        runner = CliRunner()
        command_args = list(self.DEFAULT_ARGS)
        command_args += ["-l", "some-licensee1"]
        command_args += ["-l", "some-licensee2"]
        result = runner.invoke(cli, command_args)
        if result.exception:
            LOGGER.info(result.exception)
        self.assertEqual(self.mock_run_icepick.call_count, 2)
        assert result.exit_code == 0

    @patch(
        "dice_elipy_scripts.icepick_run.import_avalanche_data_state",
        MagicMock(return_value=["-importState", "db_name"]),
    )
    @patch("dice_elipy_scripts.icepick_run.filer", MagicMock())
    @patch("dice_elipy_scripts.icepick_run.SETTINGS", ConfigManager(path=config_path))
    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo", MagicMock())
    @patch("os.path.join", MagicMock(side_effect=join))
    def test_run_icepick_cooktype_avalanche_storage(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.VALUE_PLATFORM,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_ASSETS,
                self.VALUE_ASSETS,
                self.OPTION_DATA_BRANCH,
                "dev-na",
                self.OPTION_TEST_EXTRA_ARG,
                self.VALUE_TEST_EXTRA_ARG,
                self.OPTION_CLIENT_BUILD_ID,
                self.VALUE_CLIENT_BUILD_ID,
                self.OPTION_COOK_TYPE,
                "avalanche_storage",
                "--dnp",
                "ExampleGame",
            ],
        )
        if result.exception:
            LOGGER.info(result.exception)
        self.assertEqual(self.mock_run_icepick.call_count, 1)
        kwargs = {
            "platform": self.VALUE_PLATFORM,
            "test_suite": "\\some\\path\\" + self.VALUE_TEST_SUITE_NAME1,
            "test_group": "nogroup",
            "config": "final",
            "settings_file_list": [],
            "send_frosting_report": True,
            "lease": None,
            "build_type": "static",
            "autobuild": False,
            "run_args": [
                self.OPTION_TEST_EXTRA_ARG,
                self.VALUE_TEST_EXTRA_ARG,
                "--storage-server",
                "avalanche.state.host",
                "--databaseId",
                "temp_db_name",
                "--pool-type",
                self.VALUE_POOL_TYPE1,
            ],
            "ignore_icepick_exit_code": False,
            "cook": False,
            "extra_framework_args": None,
            "custom_test_suite_data": "code_changelist:123;data_changelist:123;",
        }
        self.mock_run_icepick.assert_called_once_with(**kwargs)
        assert result.exit_code == 0

    @patch(
        "dice_elipy_scripts.icepick_run.import_avalanche_data_state",
        MagicMock(return_value=["-importState", "db_name"]),
    )
    @patch("dice_elipy_scripts.icepick_run.filer", MagicMock())
    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo", MagicMock())
    @patch("os.path.join", MagicMock(side_effect=join))
    def test_run_icepick_battlefield_test_suite(self):
        with patch(
            "dice_elipy_scripts.icepick_run.frostbite_core.get_licensee_id",
            MagicMock(return_value="battlefieldgame"),
        ):
            runner = CliRunner()
            result = runner.invoke(cli, self.DEFAULT_ARGS)
            kwargs = {
                "platform": self.VALUE_PLATFORM,
                "test_suite": self.VALUE_TEST_SUITE_NAME1,
                "test_group": "nogroup",
                "config": "final",
                "settings_file_list": [],
                "send_frosting_report": True,
                "lease": None,
                "build_type": "static",
                "autobuild": False,
                "run_args": [
                    self.OPTION_TEST_EXTRA_ARG,
                    self.VALUE_TEST_EXTRA_ARG,
                    "--pool-type",
                    self.VALUE_POOL_TYPE1,
                ],
                "ignore_icepick_exit_code": False,
                "cook": False,
                "extra_framework_args": None,
                "custom_test_suite_data": "code_changelist:123;data_changelist:123;",
            }
            self.mock_run_icepick.assert_any_call(**kwargs)
            kwargs["test_suite"] = self.VALUE_TEST_SUITE_NAME2
            kwargs["run_args"] = [
                self.OPTION_TEST_EXTRA_ARG,
                self.VALUE_TEST_EXTRA_ARG,
                "--pool-type",
                self.VALUE_POOL_TYPE2,
            ]
            self.mock_run_icepick.assert_called_with(**kwargs)
            assert result.exit_code == 0

    @patch(
        "dice_elipy_scripts.icepick_run.import_avalanche_data_state",
        MagicMock(return_value=["-importState", "db_name"]),
    )
    @patch("dice_elipy_scripts.icepick_run.filer", MagicMock())
    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo", MagicMock())
    @patch("os.path.join", MagicMock(side_effect=join))
    def test_run_icepick_casablanca_test_suite(self):
        with patch(
            "dice_elipy_scripts.icepick_run.frostbite_core.get_licensee_id",
            MagicMock(return_value="casablanca"),
        ):
            runner = CliRunner()
            result = runner.invoke(cli, self.DEFAULT_ARGS)
            kwargs = {
                "platform": self.VALUE_PLATFORM,
                "test_suite": self.VALUE_TEST_SUITE_NAME1,
                "test_group": "nogroup",
                "config": "final",
                "settings_file_list": [],
                "send_frosting_report": True,
                "lease": None,
                "build_type": "static",
                "autobuild": True,
                "run_args": [
                    self.OPTION_TEST_EXTRA_ARG,
                    self.VALUE_TEST_EXTRA_ARG,
                    "--pool-type",
                    self.VALUE_POOL_TYPE1,
                ],
                "ignore_icepick_exit_code": False,
                "cook": False,
                "extra_framework_args": None,
                "custom_test_suite_data": "code_changelist:123;data_changelist:123;",
            }
            self.mock_run_icepick.assert_any_call(**kwargs)
            kwargs["test_suite"] = self.VALUE_TEST_SUITE_NAME2
            kwargs["run_args"] = [
                self.OPTION_TEST_EXTRA_ARG,
                self.VALUE_TEST_EXTRA_ARG,
                "--pool-type",
                self.VALUE_POOL_TYPE2,
            ]
            self.mock_run_icepick.assert_called_with(**kwargs)
            assert result.exit_code == 0

    @patch(
        "dice_elipy_scripts.icepick_run.import_avalanche_data_state",
        MagicMock(return_value=["-importState", "db_name"]),
    )
    @patch("dice_elipy_scripts.icepick_run.filer", MagicMock())
    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo", MagicMock())
    @patch("os.path.join", MagicMock(side_effect=join))
    def test_run_icepick_custom_test_suite_data(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.VALUE_PLATFORM,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES2,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_ASSETS,
                self.VALUE_ASSETS,
                self.OPTION_DATA_BRANCH,
                self.VALUE_DATA_BRANCH,
                self.OPTION_TEST_EXTRA_ARG,
                self.VALUE_TEST_EXTRA_ARG,
                self.OPTION_CLIENT_BUILD_ID,
                self.VALUE_CLIENT_BUILD_ID,
                self.OPTION_CLIENT_BUILD_ID,
                "\\\\client\\build\\id_1",
                "--ctsd",
                "mykey:myvalue",
            ],
        )
        if result.exception:
            LOGGER.info(result.exception)
        self.assertEqual(self.mock_run_icepick.call_count, 2)
        kwargs = {
            "platform": self.VALUE_PLATFORM,
            "test_suite": "\\some\\path\\" + self.VALUE_TEST_SUITE_NAME1,
            "test_group": "nogroup",
            "config": "final",
            "settings_file_list": [],
            "send_frosting_report": True,
            "lease": None,
            "build_type": "static",
            "autobuild": False,
            "run_args": [
                self.OPTION_TEST_EXTRA_ARG,
                self.VALUE_TEST_EXTRA_ARG,
                "--pool-type",
                self.VALUE_POOL_TYPE1,
            ],
            "ignore_icepick_exit_code": False,
            "cook": False,
            "extra_framework_args": None,
            "custom_test_suite_data": "code_changelist:123;data_changelist:123;mykey:myvalue;",
        }
        self.mock_run_icepick.assert_any_call(**kwargs)
        kwargs["test_suite"] = "\\some\\path\\" + self.VALUE_TEST_SUITE_NAME2
        kwargs["run_args"] = [
            self.OPTION_TEST_EXTRA_ARG,
            self.VALUE_TEST_EXTRA_ARG,
            "--pool-type",
            self.VALUE_POOL_TYPE2,
        ]
        self.mock_run_icepick.assert_called_with(**kwargs)
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.icepick_run.avalanche", MagicMock())
    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo", MagicMock())
    @patch("dice_elipy_scripts.icepick_run.filer.FilerUtils", MagicMock())
    def test_icepick_failure_does_not_fail_command(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)

        self.mock_run_icepick.side_effect = Exception("failing build")
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.icepick_run.avalanche", MagicMock())
    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo", MagicMock())
    @patch("dice_elipy_scripts.icepick_run.filer.FilerUtils", MagicMock())
    @patch("elipy2.frostbite.fbenv_layer.is_api_function_failed_exception")
    def test_icepick_fbenv_exception(self, mock_exception):
        mock_exception.return_value = True
        runner = CliRunner()

        self.mock_run_icepick.side_effect = Exception("failing build")
        result = runner.invoke(cli, self.DEFAULT_ARGS)

        assert result.exit_code == 1

    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo", MagicMock())
    @patch("dice_elipy_scripts.icepick_run.filer.FilerUtils", return_value=MagicMock())
    def test_download_server_binaries(self, patch_filer):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_NEED_GAME_SERVER,
                True,
                self.OPTION_CONFIG,
                self.VALUE_CONFIG,
                self.OPTION_SERVER_CONFIG,
                self.VALUE_SERVER_CONFIG,
                self.OPTION_SERVER_BUILD_ID,
                self.VALUE_SERVER_BUILD_ID,
            ],
        )

        patch_filer.return_value.fetch_code.assert_called_with(
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            self.VALUE_SERVER_PLATFORM,
            self.VALUE_SERVER_CONFIG,
            custom_tag=None,
        )
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo", MagicMock())
    @patch("dice_elipy_scripts.icepick_run.filer.FilerUtils", return_value=MagicMock())
    def test_fetch_code_custom_tag(self, mock_filer):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_CONFIG,
                self.VALUE_CONFIG,
                self.OPTION_custom_tag,
                self.VALUE_custom_tag,
            ],
        )

        mock_filer.return_value.fetch_code.assert_called_with(
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            self.VALUE_PLATFORM,
            self.VALUE_CONFIG,
            fetch_tests=False,
            custom_tag=self.VALUE_custom_tag,
            mirror=True,
        )
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo", MagicMock())
    @patch("dice_elipy_scripts.icepick_run.filer.FilerUtils", return_value=MagicMock())
    def test_does_not_download_server_binaries(self, patch_filer):
        runner = CliRunner()
        runner.invoke(cli, self.DEFAULT_ARGS)

        assert patch_filer.return_value.fetch_code.call_count == 2

    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo", MagicMock())
    @patch("dice_elipy_scripts.icepick_run.filer.FilerUtils", return_value=MagicMock())
    @patch(
        "dice_elipy_scripts.icepick_run.import_avalanche_data_state",
        return_value=MagicMock(),
    )
    def test_import_avalanche(self, patch_avalanche_import, patch_filer):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_IMPORT_AVALANCHE_STATE, "true"]
        )

        patch_avalanche_import.assert_called_with(
            "data-branch", "code-branch", "ps4", patch_filer.return_value, "123"
        )
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo", MagicMock())
    @patch("dice_elipy_scripts.icepick_run.filer.FilerUtils", MagicMock())
    @patch(
        "dice_elipy_scripts.icepick_run.import_avalanche_data_state",
        return_value=MagicMock(),
    )
    def test_does_not_import_avalanche(self, patch_avalanche_import):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert patch_avalanche_import.call_count == 0

    @patch(
        "dice_elipy_scripts.icepick_run.import_avalanche_data_state",
        return_value=MagicMock(),
    )
    @patch(
        "dice_elipy_scripts.icepick_run.get_build_from_bilbo",
        MagicMock(
            return_value={"type": "frosty", "platform": "ps5", "source": "\\\\ps5\\build\\id"}
        ),
    )
    @patch("dice_elipy_scripts.icepick_run.filer.FilerUtils", return_value=MagicMock())
    @patch("os.path.join", MagicMock(side_effect=join))
    def test_fetch_frosty_server(self, patch_filer, patch_avalanche_import):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_SERVER_REGION,
                self.VALUE_SERVER_REGION,
                self.OPTION_PACKAGE_TYPE,
                self.VALUE_PACKAGE_TYPE,
                self.OPTION_NEED_GAME_SERVER,
                "true",
                self.OPTION_SERVER_BUILD_ID,
                self.VALUE_SERVER_BUILD_ID,
            ],
        )

        assert result.exit_code == 0

    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo")
    def test_get_client_build_from_bilbo(self, patch_get_build_from_bilbo):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.VALUE_PLATFORM,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES2,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_ASSETS,
                self.VALUE_ASSETS,
                self.OPTION_DATA_BRANCH,
                self.VALUE_DATA_BRANCH,
                self.OPTION_TEST_EXTRA_ARG,
                self.VALUE_TEST_EXTRA_ARG,
                self.OPTION_CLIENT_BUILD_ID,
                "\\\\client\\build\\id,\\\\client\\build\\id_1",
            ],
        )

        patch_get_build_from_bilbo.assert_any_call(self.VALUE_CLIENT_BUILD_ID, False, False, False)
        assert patch_get_build_from_bilbo.call_count == 2

    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo")
    def test_get_server_build_from_bilbo(self, patch_get_build_from_bilbo):
        frosty_type = "frosty"
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_SERVER_REGION,
                self.VALUE_SERVER_REGION,
                self.OPTION_PACKAGE_TYPE,
                self.VALUE_PACKAGE_TYPE,
                self.OPTION_REGION,
                self.VALUE_REGION,
                self.OPTION_NEED_GAME_SERVER,
                "true",
                self.OPTION_SERVER_CONFIG,
                self.VALUE_SERVER_CONFIG,
                self.OPTION_SERVER_BUILD_ID,
                self.VALUE_SERVER_BUILD_ID,
            ],
        )

        patch_get_build_from_bilbo.assert_any_call(self.VALUE_CLIENT_BUILD_ID, False, False, False)
        patch_get_build_from_bilbo.assert_called_with(
            self.VALUE_SERVER_BUILD_ID, False, False, False
        )

        assert patch_get_build_from_bilbo.call_count == 2

    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo")
    def test_get_use_is_frosted_builds(self, patch_get_build_from_bilbo):
        runner = CliRunner()
        runner.invoke(cli, self.DEFAULT_ARGS + ["--is-frosted", "true"])
        patch_get_build_from_bilbo.assert_called_once_with(
            self.VALUE_CLIENT_BUILD_ID, False, False, True
        )

    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo")
    def test_get_use_shift_builds(self, patch_get_build_from_bilbo):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + ["--use-shift-build", "true"])
        patch_get_build_from_bilbo.assert_called_once_with(
            self.VALUE_CLIENT_BUILD_ID, True, False, False
        )

    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo")
    def test_get_use_spin_builds(self, patch_get_build_from_bilbo):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + ["--use-spin-build", "true"])
        patch_get_build_from_bilbo.assert_called_once_with(
            self.VALUE_CLIENT_BUILD_ID, False, True, False
        )

    @patch("elipy2.SETTINGS.get", MagicMock(return_value="\\fake_path\\here"))
    @patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo", return_value=MagicMock())
    @patch("dice_elipy_scripts.icepick_run.filer.FilerUtils", return_value=MagicMock())
    @patch("elipy2.core.is_buildsystem_run", MagicMock(return_value=True))
    def test_filer_authentication(self, patch_filer_utils, patch_get_build_from_bilbo):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + ["--filer-user", "user", "--filer-password", "password"]
        )
        assert result.exit_code == 0
        patch_filer_utils.return_value.delete_network_connection.assert_called_once()
        patch_filer_utils.return_value.auth_network_connection.assert_called_once_with(
            network_path="\\fake_path\\here",
            username="user",
            password="password",
        )

    def test_skip_frosty_build_with_spin_build(self):
        with patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo") as mock_get_build, patch(
            "dice_elipy_scripts.icepick_run.filer.FilerUtils"
        ) as mock_filer_class, patch(
            "dice_elipy_scripts.icepick_run.frosty_build_utils"
        ) as mock_frosty_build_utils:

            # Mock the build response
            mock_build = {"type": "frosty", "platform": "ps4"}
            mock_get_build.return_value = mock_build

            # Create mock for filer
            mock_filer = MagicMock()
            mock_filer_class.return_value = mock_filer

            # Run command with spin build flag
            runner = CliRunner(mix_stderr=False)

            # Add additional patches to prevent exceptions
            with patch(
                "dice_elipy_scripts.icepick_run.os.path.join", return_value="\\some\\path"
            ), patch(
                "dice_elipy_scripts.icepick_run.build_metadata_utils.setup_metadata_manager",
                return_value=MagicMock(),
            ), patch(
                "dice_elipy_scripts.icepick_run.autotest_utils.get_test_suites_names",
                return_value=["test-suite1"],
            ), patch(
                "dice_elipy_scripts.icepick_run.time", MagicMock()
            ), patch(
                "dice_elipy_scripts.icepick_run.avalanche", MagicMock()
            ):
                result = runner.invoke(
                    cli,
                    self.DEFAULT_ARGS
                    + [
                        "--use-spin-build",
                        "true",
                    ],
                )

        assert result.exit_code == 0
        mock_filer.fetch_frosty_build_by_source.assert_not_called()
        mock_frosty_build_utils.install_required_sdks.assert_not_called()


class TestGetBuildFromBilbo:
    build_id = "\\mock\\build\\id"
    valid_code_changelist = "123"
    valid_data_changelist = "123"
    platform = "ps5"
    region = "dev"
    source = "\\not\\a\\real\\source"
    deleted = "2022-10-07T08:02:51.898876"
    frosty_config = "final"
    frosty_package_type = "files"
    frosty_type = "frosty"
    additionals_configs = ["performance"]

    def test_check_frosty_build_exists(self, fixture_metadata_manager):
        builds = [
            Build.from_dict(
                {
                    "_id": self.build_id,
                    "_source": {
                        "code_changelist": self.valid_code_changelist,
                        "data_changelist": self.valid_data_changelist,
                        "platform": self.platform,
                        "region": self.region,
                        "config": self.frosty_config,
                        "additional_configs": self.additionals_configs,
                        "package_type": self.frosty_package_type,
                        "source": self.source,
                    },
                }
            )
        ]
        fixture_metadata_manager.get_build_by_id.return_value = builds
        build = get_build_from_bilbo(self.build_id)

        assert build["code_changelist"] == self.valid_code_changelist
        assert build["data_changelist"] == self.valid_data_changelist
        assert build["platform"] == self.platform
        assert build["region"] == self.region
        assert build["config"] == self.frosty_config
        assert build["package_type"] == self.frosty_package_type
        assert build["source"] == self.source

    def test_check_deleted_frosty_build(self, fixture_metadata_manager):
        builds = [
            Build.from_dict(
                {
                    "_id": self.build_id,
                    "_source": {
                        "code_changelist": self.valid_code_changelist,
                        "data_changelist": self.valid_data_changelist,
                        "platform": self.platform,
                        "region": self.region,
                        "config": self.frosty_config,
                        "package_type": self.frosty_package_type,
                        "source": self.source,
                        "deleted": self.deleted,
                    },
                }
            )
        ]
        fixture_metadata_manager.get_build_by_id.return_value = builds
        with TestCase.assertRaisesRegex(self, ELIPYException, "All builds were deleted."):
            get_build_from_bilbo(self.build_id)

    def test_check_frosty_build_doesnt_exist(self, fixture_metadata_manager):
        fixture_metadata_manager.get_build_by_id.side_effect = ELIPYException(
            "No builds found in Bilbo."
        )
        with TestCase.assertRaisesRegex(self, ELIPYException, "No builds found in Bilbo."):
            get_build_from_bilbo(self.build_id)

    def test_check_use_shift(self, fixture_metadata_manager):
        builds = [
            Build.from_dict(
                {
                    "_id": self.build_id,
                    "_source": {
                        "code_changelist": self.valid_code_changelist,
                        "data_changelist": self.valid_data_changelist,
                        "platform": self.platform,
                        "region": self.region,
                        "config": self.frosty_config,
                        "package_type": self.frosty_package_type,
                        "source": self.source,
                        "deleted": self.deleted,
                        "shift": {"uploaded": "2022-10-06 19:18"},
                    },
                }
            )
        ]
        fixture_metadata_manager.get_build_by_id.return_value = builds
        build = get_build_from_bilbo(self.build_id, use_shift_build=True)

        assert "shift" in build.keys()

    def test_check_use_spin(self, fixture_metadata_manager):
        builds = [
            Build.from_dict(
                {
                    "_id": self.build_id,
                    "_source": {
                        "code_changelist": self.valid_code_changelist,
                        "data_changelist": self.valid_data_changelist,
                        "platform": self.platform,
                        "region": self.region,
                        "config": self.frosty_config,
                        "package_type": self.frosty_package_type,
                        "source": self.source,
                        "deleted": self.deleted,
                        "spin": {"uploaded": "2022-10-06 19:18"},
                    },
                }
            )
        ]
        fixture_metadata_manager.get_build_by_id.return_value = builds
        build = get_build_from_bilbo(self.build_id, use_spin_build=True)

        assert "spin" in build.keys()

    def test_check_drone_build_exist(self, fixture_metadata_manager):
        builds = [
            Build.from_dict(
                {
                    "_id": self.build_id,
                    "_source": {
                        "changelist": self.valid_code_changelist,
                        "verified_data": [
                            {
                                "changelist": self.valid_data_changelist,
                            },
                        ],
                        "source": self.source,
                    },
                }
            )
        ]
        fixture_metadata_manager.get_build_by_id.return_value = builds
        build = get_build_from_bilbo(self.build_id)

        assert build["changelist"] == self.valid_code_changelist
        assert build["verified_data"][0]["changelist"] == self.valid_data_changelist
        assert build["source"] == self.source


@patch(
    "dice_elipy_scripts.icepick_run.import_avalanche_data_state",
    return_value=["-importState", "db_name"],
    autospec=True,
)
@patch("dice_elipy_scripts.icepick_run.avalanche", autospec=True)
@patch("dice_elipy_scripts.icepick_run.frosty_build_utils", autospec=True)
@patch("dice_elipy_scripts.icepick_run.get_build_from_bilbo", autospec=True)
@patch("dice_elipy_scripts.icepick_run.get_environment_variables", MagicMock())
@patch("dice_elipy_scripts.icepick_run.data", autospec=True)
@patch("dice_elipy_scripts.icepick_run.autotest_utils", autospec=True)
@patch("dice_elipy_scripts.icepick_run.filer", autospec=True)
@patch("dice_elipy_scripts.utils.icepick_utils.core.robocopy", autospec=True)
class TestHailstormOptions:
    @patch("dice_elipy_scripts.icepick_run.icepick")
    def test_run_icepick_disable_hailstorm(self, patch_icepick: MagicMock, *_):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            TestIcepickBase.DEFAULT_ARGS
            + [
                TestIcepickBase.OPTION_COOK_TYPE,
                "icepick_cook",
                TestIcepickBase.BOOL_ENABLE_HAILSTORM,
                "False",
            ],
        )

        assert result.exit_code == 0
        patch_icepick.IcepickUtils.run_icepick_cook.assert_called()

        args, kwargs = patch_icepick.IcepickUtils.run_icepick_cook.call_args
        assert kwargs.get("enable_hailstorm") is False


class TestGetBuildFromBilbo:
    def test_get_build_from_bilbo_none_id(self):
        result = get_build_from_bilbo(None)
        assert result == {}

    def test_get_build_from_bilbo_is_frosted(self):
        with pytest.raises(IndexError):
            get_build_from_bilbo("some id", is_frosted=True)

    def test_get_build_from_bilbo_is_shift(self):
        with pytest.raises(IndexError):
            get_build_from_bilbo("some id", use_shift_build=True)

    def test_get_build_from_bilbo_is_spin(self):
        with pytest.raises(IndexError):
            get_build_from_bilbo("some id", use_spin_build=True)


@patch("dice_elipy_scripts.icepick_run.save_icepick_logs", MagicMock())
@patch("dice_elipy_scripts.icepick_run.sync_specified_files_to_head", MagicMock())
@patch("dice_elipy_scripts.icepick_run.add_sentry_tags", MagicMock())
@patch("dice_elipy_scripts.icepick_run.get_environment_variables", MagicMock())
@patch("dice_elipy_scripts.icepick_run.data.DataUtils", MagicMock())
@patch.multiple(
    "dice_elipy_scripts.icepick_run.frostbite_core",
    minimum_fb_version=MagicMock(return_value=True),
    get_game_data_dir=MagicMock(return_value="\\some\\path"),
    get_licensee_id=MagicMock(return_value="some-licensee"),
)
@patch("elipy2.build_metadata.BuildMetadataManager", MagicMock())
@patch("dice_elipy_scripts.utils.autotest_utils.filer_paths", MagicMock())
@patch("dice_elipy_scripts.icepick_run.frosty_build_utils")
@patch("dice_elipy_scripts.icepick_run.avalanche")
@patch("dice_elipy_scripts.icepick_run.set_licensee", MagicMock())
@patch("dice_elipy_scripts.icepick_run.icepick_clean", MagicMock())
@patch("dice_elipy_scripts.icepick_run.filer", MagicMock())
@patch(
    "dice_elipy_scripts.icepick_run.get_build_from_bilbo",
    MagicMock(return_value={"type": "frosty", "platform": "ps4"}),
)
class TestSpinBuildSDKSkip(TestCase):
    """Test class specifically for testing the behavior of SDK installation with Spin builds."""

    def test_skip_sdk_install_with_spin_build(self, mock_avalanche, mock_frosty_build_utils):
        """Test that SDK installation is skipped when use_spin_build is True."""
        # Setup
        runner = CliRunner(mix_stderr=False)
        args = [
            "ps4",
            "--ts",
            '{"name": "test-suite1","extra-args":["--pool-type","atf"]}',
            "--cb",
            "code-branch",
            "--cc",
            "123",
            "--dc",
            "123",
            "--as",
            "asset1",
            "--db",
            "data-branch",
            "--cbi",
            "\\\\client\\build\\id",
            "--use-spin-build",
            "True",  # This flag means the SDK installation should be skipped
        ]

        # Execute
        with patch(
            "dice_elipy_scripts.icepick_run.os.path.join", return_value="\\some\\path"
        ), patch(
            "dice_elipy_scripts.icepick_run.build_metadata_utils.setup_metadata_manager",
            return_value=MagicMock(),
        ), patch(
            "dice_elipy_scripts.icepick_run.autotest_utils.get_test_suites_names",
            return_value=["test-suite1"],
        ), patch(
            "dice_elipy_scripts.icepick_run.time", MagicMock()
        ), patch(
            "dice_elipy_scripts.icepick_run.get_build_from_bilbo",
            return_value={"type": "frosty", "platform": "ps4", "spin": {"uploaded": "2023-01-01"}}
        ):
            result = runner.invoke(cli, args)

        # Verify that SDK installation was skipped
        mock_frosty_build_utils.install_required_sdks.assert_not_called()
        # Avalanche restart should still happen regardless of spin build setting
        mock_avalanche.restart_avalanche.assert_called_once()

    def test_normal_sdk_install_without_spin_build(self, mock_avalanche, mock_frosty_build_utils):
        """Test that SDK installation is performed when use_spin_build is False (default)."""
        # Setup
        runner = CliRunner(mix_stderr=False)
        args = [
            "ps4",
            "--ts",
            '{"name": "test-suite1","extra-args":["--pool-type","atf"]}',
            "--cb",
            "code-branch",
            "--cc",
            "123",
            "--dc",
            "123",
            "--as",
            "asset1",
            "--db",
            "data-branch",
            "--cbi",
            "\\\\client\\build\\id",
            # No use-spin-build flag, so SDK installation should happen by default
        ]

        # Execute
        with patch(
            "dice_elipy_scripts.icepick_run.os.path.join", return_value="\\some\\path"
        ), patch(
            "dice_elipy_scripts.icepick_run.build_metadata_utils.setup_metadata_manager",
            return_value=MagicMock(),
        ), patch(
            "dice_elipy_scripts.icepick_run.autotest_utils.get_test_suites_names",
            return_value=["test-suite1"],
        ), patch(
            "dice_elipy_scripts.icepick_run.time", MagicMock()
        ), patch(
            "dice_elipy_scripts.icepick_run.get_build_from_bilbo",
            return_value={"type": "frosty", "platform": "ps4"}  # No spin flag in this build
        ):
            result = runner.invoke(cli, args)

        # Verify that SDK installation was called
        mock_frosty_build_utils.install_required_sdks.assert_called_once()
        # Avalanche restart should happen in all cases
        mock_avalanche.restart_avalanche.assert_called_once()
